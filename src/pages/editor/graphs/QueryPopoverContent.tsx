import React, { useMemo, useState } from 'react';
import ReactCode<PERSON>irror from '@uiw/react-codemirror';
import useEditorTheme from '@/pages/editor/useEditorTheme';
import { gsql } from '@/pages/editor/GSQL/script';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import {
  canDropQuery,
  canEditQuery,
  canInstallQuery,
  canReadQuery,
  QueryMetaLogic,
  QueryParam,
} from '@tigergraph/tools-models';
import { MdDeleteOutline, MdEdit, MdUpload } from 'react-icons/md';
import { EditorView } from '@codemirror/view';
import { CreateTempFileFn } from '@/pages/editor/file/hooks';
import { lintQueryCode } from '@/pages/editor/graphs/util';
import { useEditorContext } from '@/contexts/graphEditorContext';
import { useQueryClient } from 'react-query';
import DeleteQueryModal from '@/pages/editor/graphs/DeleteQueryModal';
import ConfirmModal from '@/components/ConfirmModal';
import { showToast } from '@tigergraph/app-ui-lib/styledToasterContainer';
import { Input } from '@tigergraph/app-ui-lib/input';
import { RenameIcon } from '@/pages/home/<USER>';
import { canRunQuery, FormatValidator, GsqlQueryMeta } from '@tigergraph/tools-models';
import { getQueryContentUnderNewName, getQueryStatus, QueryStatus } from '@/utils/query';
import IconButton from '@/components/IconButton';
import { PLACEMENT } from 'baseui/popover';
import StatefulPopover from '@/pages/editor/StatefulPopover';
import { ListItem } from '@/components/Expandable';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { IoPlaySharp } from 'react-icons/io5';
import { TableBuilder } from '@tigergraph/app-ui-lib/table';
import { expand } from 'inline-style-expand-shorthand';
import { TableBuilderColumn } from 'baseui/table-semantic';
import { getParamTypeDisplayName } from '@/utils/queryParam';
import { isDistributedQuery } from '@/pages/editor/query/util';
import RunQueryDrawer from '@/pages/editor/query/RunQueryDrawer';

interface QueryPopoverContentProps {
  query: GsqlQueryMeta;
  graphName: string;
  createTempFile: CreateTempFileFn;
  onClose?: () => void;
}

export default function QueryPopoverContent({ query, graphName, createTempFile, onClose }: QueryPopoverContentProps) {
  const queryClient = useQueryClient();
  const { currentWorkspace, dbUser } = useWorkspaceContext();
  const { runCmd, isCommandRunning, currentFileId } = useEditorContext();
  const workspace = currentWorkspace!;

  const [css, theme] = useStyletron();

  const editorTheme = useEditorTheme();
  const cmExtensions = useMemo(() => {
    // Encounter an issue when loading extensions in test environment
    //  Error: Unrecognized extension value in extension set ([object Object]).
    if (import.meta.env.VITEST) {
      return [];
    }

    return [gsql()];
  }, []);

  const queryParams: QueryParam[] = useMemo(() => {
    const params = query.endpoint?.query?.[graphName]?.[query.name]?.['GET/POST']?.parameters || {};
    return QueryMetaLogic.convertGSQLParameters(params);
  }, [query, graphName]);

  const canEdit = canEditQuery(dbUser!.getUesrInfo(), graphName, query.name, workspace.tg_version);
  const canView = canReadQuery(dbUser!.getUesrInfo(), graphName, query.name, workspace.tg_version);

  const [linting, setLinting] = useState(false);
  const installQuery = async (query: GsqlQueryMeta) => {
    setLinting(true);
    let lintPass = true;
    try {
      lintPass = await lintQueryCode(workspace, graphName, query);
    } catch (error) {
      //
    }
    setLinting(false);
    if (!lintPass) {
      return;
    }

    // Install query with -SINGLE flag if it is not a distributed query
    // to improve the performance and enable query profiling
    const cmdText =
      `USE GRAPH ${graphName}\n` + `INSTALL QUERY ${isDistributedQuery(query.code) ? '' : '-SINGLE '}${query.name}`;
    queryClient.setQueryData(['queries', workspace.workspace_id, graphName], (oldData: GsqlQueryMeta[] | undefined) => {
      return (oldData || []).map((oldQuery) => {
        if (oldQuery.name === query.name) {
          return { ...oldQuery, installing: true };
        }
        return oldQuery;
      });
    });
    runCmd(workspace, graphName, cmdText);
  };

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const deleteQuery = async (query: GsqlQueryMeta) => {
    const cmdText = `USE GRAPH ${graphName}\nDROP QUERY ${query.name}`;
    runCmd(workspace, graphName, cmdText);
  };

  const [tempName, setTempName] = useState<string>('');
  const [isEditingName, setIsEditingName] = useState<boolean>(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const handleRenameQuery = () => {
    setIsEditingName(false);
    const newName = tempName.trim();
    if (!newName || newName === query.name) {
      return;
    }

    const result = FormatValidator.isName(newName);
    if (!result.success) {
      showToast({ kind: 'negative', message: 'Invalid query name.' });
      return;
    }

    const queries = queryClient.getQueryData(['queries', workspace.workspace_id, graphName]) as GsqlQueryMeta[];
    if (queries.find((q) => q.name === newName)) {
      showToast({ kind: 'negative', message: 'Query name already exists.' });
      return;
    }

    setRenameDialogOpen(true);
  };
  const renameQuery = (query: GsqlQueryMeta, newName: string) => {
    let cmdText = `USE GRAPH ${graphName}\n`;
    cmdText += `${getQueryContentUnderNewName(query, tempName)}\n`;
    cmdText += `DROP QUERY ${query.name}`;
    runCmd(workspace, graphName, cmdText);
  };

  const [showRunDrawer, setShowRunDrawer] = useState(false);

  const operations = [
    {
      label: 'Play',
      icon: <IoPlaySharp size={16} color={theme.colors['button.icon']} />,
      handleFn: () => {
        setShowRunDrawer(true);
      },
      disabled: !canRunQuery(dbUser!.getUesrInfo(), graphName, query.name, workspace.tg_version),
    },
    {
      label: 'Edit',
      icon: <MdEdit size={16} color={theme.colors['button.icon']} />,
      handleFn: () => {},
      disabled: !canEdit,
    },
    {
      label: 'Install',
      icon: <MdUpload size={16} color={theme.colors['button.icon']} />,
      handleFn: () => installQuery(query),
      disabled:
        !canInstallQuery(dbUser!.getUesrInfo(), graphName, query.name, workspace.tg_version) ||
        query.installing ||
        query.installed ||
        isCommandRunning ||
        linting,
    },
    {
      label: 'Delete',
      icon: <MdDeleteOutline size={16} color={theme.colors['button.icon.danger']} />,
      handleFn: () => {
        setShowDeleteModal(true);
      },
      disabled:
        !canDropQuery(dbUser!.getUesrInfo(), graphName, query.name, workspace.tg_version) ||
        query.installing ||
        isCommandRunning,
    },
  ];

  const handleEditInNewFile = () => {
    createTempFile(true, query.name, query.code, graphName);
    onClose?.();
  };

  const handleCopyToCurrentFile = () => {
    const cmView = (window as any).cmView as EditorView;
    if (cmView) {
      cmView.dispatch({
        changes: {
          from: cmView.state.doc.length,
          insert: `\n${query.code}`,
        },
      });
    }
    onClose?.();
  };

  const hasOpenedFile = currentFileId && (window as any).cmView;

  return (
    <div className={css({ display: 'flex', flexDirection: 'column', gap: '16px' })}>
      <div className={css({ display: 'flex', alignItems: 'center', gap: '8px' })}>
        <div
          className={css({
            minWidth: '40px',
            padding: '0 8px',
            height: '16px',
            background: theme.colors['background.accent.gray.bolder'],
            fontWeight: 700,
            borderRadius: '1px',
            fontSize: '10px',
            color: theme.colors['text.inverse'],
          })}
        >
          {query.syntax}
        </div>
        {isEditingName ? (
          <Input
            value={tempName}
            autoFocus
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              setTempName(() => e.target.value);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                e.stopPropagation();
                handleRenameQuery();
              }
            }}
            onBlur={handleRenameQuery}
            overrides={{
              Root: { style: { height: '20px', maxWidth: '200px' } },
              Input: { style: { fontSize: '12px' } },
            }}
          />
        ) : (
          <>
            <span className={css({ color: theme.colors['text.primary'], fontSize: '12px', fontWeight: 700 })}>
              {query.name}
            </span>
            <IconButton
              onClick={() => {
                setTempName(query.name);
                setIsEditingName(true);
              }}
            >
              <RenameIcon />
            </IconButton>
          </>
        )}
        <div
          className={css({
            color:
              getQueryStatus(query) === QueryStatus.Installed
                ? theme.colors['tag.text.success']
                : theme.colors['tag.text.default'],
            height: '16px',
            lineHeight: '16px',
            padding: '0 12px',
            background:
              getQueryStatus(query) === QueryStatus.Installed
                ? theme.colors['tag.background.success']
                : theme.colors['tag.background.default'],
            borderRadius: '20px',
            textAlign: 'center',
            fontSize: '12px',
          })}
        >
          {getQueryStatus(query)}
        </div>
      </div>

      <div className="flex gap-[8px]">
        <div className="max-w-[200px]">
          {queryParams.length > 0 && (
            <TableBuilder
              data={queryParams}
              overrides={{
                TableHeadCell: { style: { ...expand({ padding: '8px', fontSize: '12px' }) } },
                TableBodyCell: { style: { ...expand({ padding: '8px', fontSize: '12px' }) } },
              }}
            >
              <TableBuilderColumn header="Parameter">
                {(row: QueryParam) => (
                  <div className={css({ color: theme.colors['text.primary'] })}>{row.paramName}</div>
                )}
              </TableBuilderColumn>
              <TableBuilderColumn header="Type">
                {(row: QueryParam) => (
                  <div className={css({ color: theme.colors['text.primary'] })}>
                    {getParamTypeDisplayName(row.paramType)}
                  </div>
                )}
              </TableBuilderColumn>
            </TableBuilder>
          )}
        </div>

        <div className="max-w-[400px]">
          <ReactCodeMirror
            value={query.draft || query.code}
            readOnly={true}
            maxWidth={'100%'}
            height="auto"
            maxHeight="400px"
            theme={editorTheme}
            extensions={cmExtensions}
          />
        </div>
      </div>

      <div className={css({ display: 'flex', justifyContent: 'flex-end', gap: '8px' })}>
        {operations.map((op) =>
          op.label !== 'Edit' ? (
            <IconButton key={op.label} title={op.label} disabled={op.disabled} onClick={op.handleFn} type="button">
              {op.icon}
            </IconButton>
          ) : (
            <StatefulPopover
              key={op.label}
              content={({ close }) => (
                <div>
                  <ListItem
                    onClick={() => {
                      handleEditInNewFile();
                      close();
                    }}
                  >
                    Edit in New File
                  </ListItem>
                  <ListItem
                    onClick={() => {
                      handleCopyToCurrentFile();
                      close();
                    }}
                    style={{
                      opacity: hasOpenedFile ? 1 : 0.5,
                      cursor: hasOpenedFile ? 'pointer' : 'not-allowed',
                      pointerEvents: hasOpenedFile ? 'auto' : 'none',
                    }}
                  >
                    Copy to Current File
                  </ListItem>
                </div>
              )}
              placement={PLACEMENT.bottom}
              ignoreBoundary
              showArrow={false}
            >
              <IconButton title={op.label} disabled={op.disabled} type="button">
                {op.icon}
              </IconButton>
            </StatefulPopover>
          )
        )}
      </div>

      <DeleteQueryModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={() => {
          deleteQuery(query);
          setShowDeleteModal(false);
        }}
        queryName={query.name}
      />
      <ConfirmModal
        open={renameDialogOpen}
        header="Warning"
        body={
          'Renaming a query will create a new query with the updated name and remove the original. The new query is not installed.'
        }
        onConfirm={() => {
          setRenameDialogOpen(false);
          renameQuery(query, tempName.trim());
        }}
        onCancel={() => setRenameDialogOpen(false)}
      />

      <RunQueryDrawer
        isOpen={showRunDrawer}
        onClose={() => setShowRunDrawer(false)}
        query={query}
        graphName={graphName}
      />
    </div>
  );
}
